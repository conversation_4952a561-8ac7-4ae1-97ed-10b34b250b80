<template>
    <section class="container grid md:grid-cols-2 items-center gap-10 py-14">
        <div class="space-y-5">
            <AppText look="heading1" tag="h1"> {{ props.hero_banner_title || $t("home.hero.title") }} </AppText>
            <AppText look="heading5" tag="p" class="text-text-secondary">
                {{ props.hero_banner_intro || $t("home.hero.description") }}
            </AppText>

            <NuxtLink :to="exchangeUrl" class="block">
                <NButton type="primary" size="large" class="min-w-50 mt-14">{{ buttonText }} </NButton>
            </NuxtLink>
        </div>

        <div class="hidden md:block w-86 size-full aspect-1/1 m-auto relative">
            <div class="absolute inset-0 flex-center">
                <div class="overlay-banner size-1 rounded-full"></div>
            </div>
            <AppImage
                :src="props.banner_image_light"
                :alt="props.hero_banner_title + ' light'"
                class="size-full dark:hidden"
            />
            <AppImage
                :src="props.banner_image_dark"
                :alt="props.hero_banner_title + ' dark'"
                class="size-full hidden !dark:block"
            />
        </div>
    </section>

    <NuxtMarquee v-if="tickers.length > 0" class="py-6 sm:py-12 mark overflow-hidden" pause-on-click auto-fill>
        <div v-for="tick in tickers" :key="tick.id" class="ml-6 sm:ml-8 h-full w-max object-contain flex gap-3">
            <AppImage
                :src="tick?.market?.base_currency?.icon_url"
                :width="36"
                :height="36"
                :alt="tick?.market?.name"
                class="size-10 rounded-full"
            />
            <div class="uppercase">
                <AppText look="body2SmRegular" tag="p" class="flex-items gap-1">
                    <span class="mr-1">{{ tick?.market?.name }}</span>
                    <AppPercentValue :value="tick.price_change_percent" />
                </AppText>
                <AppText look="subtitle" tag="h3">{{ tick.volume }}</AppText>
                <AppText look="helpText" tag="p" class="text-text-secondary"> {{ tick.last }} USD</AppText>
            </div>
        </div>
    </NuxtMarquee>
    <!-- Fallback when no tickers are available -->
    <div v-else class="py-6 sm:py-12 overflow-hidden">
        <div class="flex gap-6 sm:gap-8 animate-pulse">
            <div v-for="i in 5" :key="i" class="flex gap-3">
                <div class="size-10 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                <div class="space-y-1">
                    <div class="h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    <div class="h-3 w-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    <div class="h-3 w-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { Homepage, HomepageTranslation } from "#shared/types/directus-types";

type Props = Homepage & HomepageTranslation & { banner_image_light: string; banner_image_dark: string };
const props = defineProps<Partial<Props>>();

const userStore = useUserStore();
const tradeStore = useTradeStore();
const publicStore = usePublicStore();
const { enabledTickers, home_feature_markets } = storeToRefs(publicStore);
const { t } = useI18n();

const buttonText = computed(() => {
    if (userStore.isAuthenticated) return t("home.hero.buttonTrade");
    else return t("home.hero.buttonRegister");
});

const exchangeUrl = computed(() => {
    if (userStore.isAuthenticated) return useGetLinkTrade(tradeStore.market);
    else return useGetLinkRegisterRedirect();
});

const tickers = computed(() => {
    return enabledTickers.value.filter((ticker) => home_feature_markets.value.includes(ticker.id)).slice(0, 5) || [];
});
</script>

<style scoped>
.overlay-banner {
    box-shadow: 0 0 250px 200px #3772ff60;
    filter: blur(100px);
}

.mark {
    @apply relative z-10;
}
.mark::before {
    @apply pointer-events-none absolute top-0 bottom-0 left-0 z-10 h-100 w-40 bg-gradient-to-r from-bg from-15% to-transparent content-[''];
}
.mark::after {
    @apply pointer-events-none absolute top-0 right-0 bottom-0 z-10 h-100 w-40 bg-gradient-to-l from-bg from-15% to-transparent content-[''];
}
</style>
