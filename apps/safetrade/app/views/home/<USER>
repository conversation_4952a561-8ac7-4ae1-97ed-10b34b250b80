<template>
    <div v-if="homeData" class="space-y-16 sm:space-y-25">
        <HeroBanner v-bind="homeData" />
        <TopCoin />
        <Figures v-bind="homeData" />
        <BannerCTADownload v-bind="homeData" />
        <Services v-bind="homeData" />
        <BannerCTAGetToken v-bind="homeData" />
        <TradeNow />
    </div>
    <AppSeo collection="homepage" />
</template>

<script setup lang="ts">
import HeroBanner from "./components/HeroBanner.vue";
import TopCoin from "./components/TopCoin.vue";
import Figures from "./components/Figures.vue";
import BannerCTADownload from "./components/BannerCTADownload.vue";
import Services from "./components/Services.vue";
import BannerCTAGetToken from "./components/BannerCTAGetToken.vue";
import TradeNow from "./components/TradeNow.vue";

import type { Homepage, HomepageTranslation } from "#shared/types/directus-types";

const { localeProperties } = useI18n();

type Response = Homepage &
    HomepageTranslation & {
        banner_image_light: string;
        banner_image_dark: string;
        banner_download_light: string;
        banner_download_dark: string;
        image_qr_download: string;
    };

const { data: homeData, error } = await useFetch<Response>(
  `/api/content/homepage/singleton/${localeProperties.value.name}`,
  {
    params: { fields: ["*", "translations.*"] },
    default: () => null
  }
)

if (error.value) {
    throw createError({
        statusCode: 500,
        statusMessage: "Failed to fetch homepage data",
    });
}
</script>
