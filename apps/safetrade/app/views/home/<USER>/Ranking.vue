<template>
    <div>
        <div class="flex-between gap-2">
            <AppText look="heading3" tag="h2"> {{ $t("home.ranking.title") }} </AppText>
            <NuxtLink :to="localePath({ name: routesName.marketsCoin, query: { tab: activeTab } })">
                <NButton icon-placement="right" class="rounded-lg">
                    <span>{{ $t("home.ranking.more") }} {{ currentTabName }}</span>
                    <template #icon>
                        <span class="i-solar:arrow-right-linear"></span>
                    </template>
                </NButton>
            </NuxtLink>
        </div>

        <div class="sm:hidden n-form-item my-5">
            <NInput v-model:value="inputText" type="text" size="small" placeholder="Search" clearable>
                <template #prefix>
                    <span class="i-solar:magnifer-linear text-text-secondary mr-2 size-4.5"></span>
                </template>
            </NInput>
        </div>

        <NTabs v-model:value="activeTab" :bar-width="30" type="line" default-value="crypto_list" class="my-5">
            <NTab v-for="tab in tabs" :key="tab.name" :name="tab.name" :tab="tab.tab" :disabled="tab.disabled">
                <span>{{ tab.tab }}</span>
            </NTab>
            <template #suffix>
                <div class="hidden sm:block n-form-item">
                    <NInput
                        v-model:value="inputText"
                        type="text"
                        size="small"
                        :placeholder="$t('fees.search')"
                        clearable
                    >
                        <template #prefix>
                            <span class="i-solar:magnifer-linear text-text-secondary mr-2 size-4.5"></span>
                        </template>
                    </NInput>
                </div>
            </template>
        </NTabs>
        <div
            v-memo="[filteredTickers.length, activeTab]"
            class="border border-bg-4 rounded-lg p-2 overflow-auto sm:(max-h-120 -full)"
        >
            <AppText
                look="body2SmRegular"
                tag="div"
                class="grid grid-cols-4 sm:grid-cols-6 gap-4 p-2 text-right text-text-secondary"
            >
                <div class="col-span-2 flex">{{ $t("home.ranking.coins") }}</div>
                <span> {{ $t("home.ranking.price") }} </span>
                <span> {{ $t("home.ranking.change") }} </span>
                <span class="hidden sm:block"> {{ $t("home.ranking.high") }} </span>
                <span class="hidden sm:block"> {{ $t("home.ranking.low") }} </span>
            </AppText>

            <template v-if="filteredTickers.length > 0">
                <template v-for="tick in filteredTickers" :key="tick.id">
                    <NuxtLink
                        :to="useGetLinkTrade(tick.market)"
                        class="grid grid-cols-4 sm:grid-cols-6 gap-3 p-2 text-right hover:bg-bg-2 rounded"
                    >
                        <div class="flex-items gap-2 col-span-2">
                            <AppImage
                                :src="tick?.market?.base_currency?.icon_url"
                                class="size-6 rounded-full"
                                :width="36"
                                :height="36"
                                loading="lazy"
                            />
                            <span class="font-semibold uppercase"> {{ tick.market?.name }} </span>
                            <span class="text-text-secondary hidden sm:block"> {{ tick.market?.base_currency?.name }}</span>
                        </div>
                        <span> {{ roundNumber(tick.last, tick.market.price_precision || 2) }} </span>
                        <AppPercentValue class="justify-end" :value="tick?.price_change_percent" />
                        <span class="hidden sm:block">
                            {{ roundNumber(tick.high, tick.market.price_precision || 2) }}
                        </span>
                        <span class="hidden sm:block">{{ roundNumber(tick.low, tick.market.price_precision) }} </span>
                    </NuxtLink>
                </template>
            </template>

            <!-- Loading state when no data is available -->
            <template v-else-if="enabledTickers.length === 0">
                <div v-for="i in 5" :key="i" class="grid grid-cols-4 sm:grid-cols-6 gap-3 p-2 animate-pulse">
                    <div class="flex-items gap-2 col-span-2">
                        <div class="size-6 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                        <div class="h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div class="hidden sm:block h-4 w-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    </div>
                    <div class="h-4 w-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    <div class="h-4 w-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    <div class="hidden sm:block h-4 w-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    <div class="hidden sm:block h-4 w-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
            </template>

            <!-- Empty state when search returns no results -->
            <AppText v-else look="body2Regular" tag="div" class="rounded p-4 text-center">
                <AppEmptyData />
            </AppText>
        </div>
    </div>
</template>

<script setup lang="ts">
import { routesName } from "@/config/page";
import { roundNumber } from "~/common";

const localePath = useLocalePath();
const publicStore = usePublicStore();
const { enabledTickers } = storeToRefs(publicStore);
const { topCoins, topGainers, topLosers, topVolume } = useCoinRanking();
const { t } = useI18n();

const activeTab = ref("crypto_list");
const inputText = ref("");

const tabs = [
    { tab: t("home.ranking.hot"), name: "crypto_list", disabled: false },
    { tab: t("home.ranking.gainers"), name: "top_gainers", disabled: false },
    { tab: t("home.ranking.losers"), name: "top_losers", disabled: false },
    { tab: t("home.ranking.volume"), name: "top_volume", disabled: false },
];

const currentTabName = computed(() => tabs.find((tab) => tab.name === activeTab.value)?.tab);

// Get the current tab's tickers
const currentTabTickers = computed(() => {
    switch (activeTab.value) {
        case "crypto_list":
            return topCoins.value;
        case "top_gainers":
            return topGainers.value;
        case "top_losers":
            return topLosers.value;
        case "top_volume":
            return topVolume.value;
        default:
            return topCoins.value;
    }
});

const filteredTickers = computed(() => {
    if (!inputText.value) {
        return currentTabTickers.value;
    }

    const searchLower = inputText.value.toLowerCase();
    return enabledTickers.value
        .filter(
            (ticker) =>
                ticker.market?.name.toLowerCase().includes(searchLower) ||
                ticker.market?.base_currency?.name.toLowerCase().includes(searchLower),
        )
        .sort((a, b) => +b.last - +a.last);
});
</script>
