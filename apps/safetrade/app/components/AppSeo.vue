<template></template>

<script setup lang="ts">
import type { ExtensionSeoMetadata } from "#shared/types/directus-types";

const { localeProperties } = useI18n();
const { collection = "global" } = defineProps<{ collection: string }>();

const { data } = await useFetch<{ seo: ExtensionSeoMetadata }>(
  `/api/content/${collection}/singleton/${localeProperties.value.name}`,
  { params: { fields: ["translations.seo"] } }
);

if (data.value?.seo) {
  useHandleSeo(data.value.seo);
}
</script>
