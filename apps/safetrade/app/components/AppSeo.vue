<template></template>

<script setup lang="ts">
import type { ExtensionSeoMetadata } from "#shared/types/directus-types";

const { localeProperties } = useI18n();
const { collection = "global" } = defineProps<{
    collection: string;
}>();

// <PERSON><PERSON> dụng onMounted để tránh hydration mismatch
onMounted(async () => {
    try {
        const { data: seoData } = await $fetch(`/api/content/${collection}/singleton/${localeProperties.value.name}`, {
            params: { fields: ["translations.seo"] },
        });

        const seo = (data as any)?.seo;
        if (seo) {
            useHandleSeo(seo);
        }
    } catch (error) {
        // Ignore SEO fetch errors
        console.warn('Failed to fetch SEO data:', error);
    }
});
</script>
