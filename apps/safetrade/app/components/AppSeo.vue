<template></template>

<script setup lang="ts">
import type { ExtensionSeoMetadata } from "#shared/types/directus-types";

const { localeProperties } = useI18n();
const { collection = "global" } = defineProps<{
    collection: string;
}>();

// Use useFetch instead of useLazyFetch to ensure data is available during SSR
const { data: seoData } = await useFetch(`/api/content/${collection}/singleton/${localeProperties.value.name}`, {
    params: { fields: ["translations.seo"] },
    transform: (data: { seo: ExtensionSeoMetadata }) => data?.seo,
    default: () => null,
});

// Handle SEO in a watcher to avoid hydration issues
watch(seoData, (newSeoData) => {
    if (newSeoData) {
        useHandleSeo(newSeoData);
    }
}, { immediate: true });
</script>
